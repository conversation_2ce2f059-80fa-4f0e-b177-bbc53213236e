import { useState, useEffect, useCallback } from 'react';
import { ThumbsUp, ThumbsDown, Meh, Sparkles, ArrowLeft, ArrowRight, ArrowUp, ArrowDown, ChevronDown, ChevronUp, Brain, Lightbulb } from 'lucide-react';

// --- components/JavaCodeHighlighter.jsx ---
const JavaCodeHighlighter = ({ code, className = "" }) => {
  if (!code) return null;

  // Enhanced text preprocessing for better visual appeal
  const preprocessText = (text) => {
    return text
      // Normalize whitespace
      .replace(/\s+/g, ' ')
      .trim()

      // Add visual separators for sections
      .replace(/\b(Key\s+(?:points?|features?|benefits?|concepts?|principles?|advantages?|disadvantages?)):\s*/gi, '\n\n🔹 **$1:**\n\n')
      .replace(/\b(Examples?):\s*/gi, '\n\n💡 **$1:**\n\n')
      .replace(/\b(Important|Note|Remember|Warning):\s*/gi, '\n\n⚠️ **$1:**\n\n')
      .replace(/\b(Summary|Conclusion):\s*/gi, '\n\n📋 **$1:**\n\n')
      .replace(/\b(Definition):\s*/gi, '\n\n📖 **$1:**\n\n')
      .replace(/\b(Syntax):\s*/gi, '\n\n⚡ **$1:**\n\n')
      .replace(/\b(Implementation|Code):\s*/gi, '\n\n💻 **$1:**\n\n')
      .replace(/\b(Usage|How to use):\s*/gi, '\n\n🎯 **$1:**\n\n')
      .replace(/\b(Best practices|Tips):\s*/gi, '\n\n✨ **$1:**\n\n')

      // Format numbered lists with better spacing
      .replace(/(\d+\.\s*)/g, '\n\n$1')

      // Format lettered lists
      .replace(/([a-zA-Z]\.\s*)/g, '\n\n$1')

      // Format bullet points
      .replace(/([-•*]\s*)/g, '\n\n$1')

      // Add spacing after sentences for better paragraph breaks
      .replace(/([.!?])\s+([A-Z])/g, '$1\n\n$2')

      // Format comparison patterns
      .replace(/\b(vs\.?|versus)\b/gi, ' 🆚 ')
      .replace(/\b(compared to|in contrast to)\b/gi, ' 🔄 ')

      // Clean up excessive newlines
      .replace(/\n\s*\n\s*\n+/g, '\n\n')
      .replace(/^\n+|\n+$/g, '');
  };

  // Enhanced Java syntax highlighting
  const highlightJava = (text) => {
    // Java keywords with semantic grouping
    const keywords = {
      access: ['public', 'private', 'protected', 'default'],
      modifiers: ['static', 'final', 'abstract', 'synchronized', 'volatile', 'transient', 'native'],
      types: ['class', 'interface', 'enum', 'void', 'int', 'String', 'boolean', 'double', 'float', 'long', 'char', 'byte', 'short'],
      control: ['if', 'else', 'for', 'while', 'do', 'switch', 'case', 'default', 'break', 'continue', 'return'],
      exception: ['try', 'catch', 'finally', 'throw', 'throws'],
      oop: ['extends', 'implements', 'import', 'package', 'new', 'this', 'super'],
      literals: ['null', 'true', 'false']
    };

    let highlighted = text;

    // Highlight keywords with different colors based on their category
    Object.entries(keywords).forEach(([category, words]) => {
      const color = {
        access: 'text-purple-400',
        modifiers: 'text-blue-400',
        types: 'text-cyan-400',
        control: 'text-pink-400',
        exception: 'text-orange-400',
        oop: 'text-green-400',
        literals: 'text-yellow-400'
      }[category];

      words.forEach(keyword => {
        const regex = new RegExp(`\\b${keyword}\\b`, 'g');
        highlighted = highlighted.replace(regex, `<span class="${color} font-semibold">${keyword}</span>`);
      });
    });

    // Highlight strings with gradient effect
    highlighted = highlighted.replace(/"([^"]*?)"/g, '<span class="text-green-400 bg-green-900/20 px-1 rounded">"$1"</span>');
    highlighted = highlighted.replace(/'([^']*?)'/g, '<span class="text-green-400 bg-green-900/20 px-1 rounded">\'$1\'</span>');

    // Highlight comments with italic style
    highlighted = highlighted.replace(/\/\/.*$/gm, '<span class="text-gray-400 italic">$&</span>');
    highlighted = highlighted.replace(/\/\*[\s\S]*?\*\//g, '<span class="text-gray-400 italic">$&</span>');

    // Highlight numbers with subtle background
    highlighted = highlighted.replace(/\b\d+\.?\d*\b/g, '<span class="text-yellow-400 bg-yellow-900/20 px-1 rounded">$&</span>');

    // Highlight method calls and class names
    highlighted = highlighted.replace(/\b([A-Z][a-zA-Z0-9]*)\b/g, '<span class="text-cyan-400 font-medium">$1</span>');

    // Highlight method parameters
    highlighted = highlighted.replace(/\(([^)]*)\)/g, (match, params) => {
      return `(<span class="text-orange-300">${params}</span>)`;
    });

    return highlighted;
  };

  // Check if the content looks like code (contains common programming patterns)
  const looksLikeCode = (text) => {
    const strongCodePatterns = [
      /\bpublic\s+class\b|\bprivate\s+\w+\b|\bpublic\s+static\s+void\b/, // Java keywords in context
      /\w+\s*\([^)]*\)\s*\{/, // method definitions
      /System\.out\.println\b|System\.err\.println\b/, // Java specific
      /import\s+[\w.]+;/, // import statements
      /\bclass\s+\w+\s*\{/, // class definitions
      /\bif\s*\([^)]+\)\s*\{|\bfor\s*\([^)]+\)\s*\{|\bwhile\s*\([^)]+\)\s*\{/, // control structures
    ];
    const weakCodePatterns = [
      /\{[\s\S]*\}/,
      /;[\s]*$/m,
      /\w+\.\w+\(/,
    ];
    const hasStrongCode = strongCodePatterns.some(pattern => pattern.test(text));
    const weakIndicatorCount = weakCodePatterns.filter(pattern => pattern.test(text)).length;
    const hasMultipleWeakIndicators = weakIndicatorCount >= 2 && text.length > 50;
    return hasStrongCode || hasMultipleWeakIndicators;
  };

  const looksLikeCSS = (text) => {
    const cssIndicators = [
      /\.[\w-]+\s*\{[\s\S]*?\}/,
      /#[\w-]+\s*\{[\s\S]*?\}/,
      /@media\s*\([^)]+\)\s*\{/,
      /@import\s+["']/,
      /@keyframes\s+[\w-]+\s*\{/,
      /\{[\s\S]*?[\w-]+:\s*[^;]+;[\s\S]*?\}/,
    ];
    const indicatorCount = cssIndicators.filter(pattern => pattern.test(text)).length;
    const strongCSSPatterns = [
      /@media\s*\([^)]+\)/,
      /@import\s+/,
      /@keyframes\s+/,
      /\.\w+\s*\{[\s\S]*?[\w-]+:\s*[^;]+;[\s\S]*?\}/,
    ];
    const hasStrongCSS = strongCSSPatterns.some(pattern => pattern.test(text));
    return hasStrongCSS || indicatorCount >= 2;
  };

  const highlightCSS = (text) => {
    let highlighted = text;
    highlighted = highlighted.replace(/([.#]?[\w-]+)(\s*\{)/g, '<span class="text-cyan-400 font-semibold">$1</span>$2');
    highlighted = highlighted.replace(/([\w-]+)(\s*:\s*)/g, '<span class="text-blue-400">$1</span><span class="text-gray-300">$2</span>');
    highlighted = highlighted.replace(/:\s*([^;]+)(;)/g, ': <span class="text-green-400">$1</span><span class="text-gray-300">$2</span>');
    highlighted = highlighted.replace(/(@[\w-]+)/g, '<span class="text-purple-400 font-semibold">$1</span>');
    highlighted = highlighted.replace(/\/\*[\s\S]*?\*\//g, '<span class="text-gray-400 italic">$&</span>');
    highlighted = highlighted.replace(/(\d+(?:\.\d+)?)(px|em|rem|%|vh|vw|deg|s|ms)/g, '<span class="text-yellow-400">$1</span><span class="text-orange-400">$2</span>');
    return highlighted;
  };

  const isCode = looksLikeCode(code);
  const isCSS = looksLikeCSS(code);

  if (isCode) {
    const codeType = isCSS ? 'CSS' : 'Java';
    const highlightFunction = isCSS ? highlightCSS : highlightJava;

    return (
      <div className={`relative bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 border-2 border-violet-600/40 rounded-2xl overflow-hidden h-full flex flex-col shadow-2xl ring-2 ring-violet-400/30 ${className}`}>
        <div className={`${isCSS ? 'bg-gradient-to-r from-blue-600 via-indigo-600 to-blue-700' : 'bg-gradient-to-r from-violet-600 via-purple-600 to-violet-700'} px-6 py-3 text-white text-sm font-bold flex items-center gap-3 flex-shrink-0 shadow-lg`}>
          <div className="flex gap-1.5">
            <div className="w-3 h-3 bg-red-400 rounded-full shadow-sm"></div>
            <div className="w-3 h-3 bg-yellow-400 rounded-full shadow-sm"></div>
            <div className="w-3 h-3 bg-green-400 rounded-full shadow-sm"></div>
          </div>
          <span className="flex items-center gap-2">
            <span className="text-lg">💻</span>
                {codeType} Code
              </span>
              <button
                onClick={() => navigator.clipboard.writeText(code)}
                className="p-1 hover:bg-white/10 rounded-lg transition-colors text-white"
                title="Copy code to clipboard"
              >
                📋 Copy
              </button>
        </div>
        <div className="flex-1 min-h-0 bg-gradient-to-br from-slate-900 to-slate-800 p-6">
          <pre className="overflow-x-auto text-base font-mono leading-loose whitespace-pre h-full rounded-xl bg-black/30 p-4 shadow-inner border border-violet-700/30">
            <code
              className="text-gray-100 block"
              style={{
                wordBreak: 'keep-all',
                overflowWrap: 'normal',
                whiteSpace: 'pre',
                lineHeight: '1.9',
                textShadow: '0 1px 2px rgba(0,0,0,0.5)'
              }}
              dangerouslySetInnerHTML={{ __html: highlightFunction(code) }}
            />
          </pre>
        </div>
      </div>
    );
  } else {
    // Regular text formatting with better typography and intelligent line breaking
    const formatTextContent = (text) => {
      // Use enhanced preprocessing for better visual appeal
      const processedText = preprocessText(text);

      const paragraphs = processedText.split(/(?:\r?\n\s*){2,}/);

      return paragraphs.map((paragraph, pIndex) => {
        const cleanParagraph = paragraph.trim();
        if (!cleanParagraph) return null;

        // Enhanced header detection with emoji support
        const isHeader = /^(🔹|💡|⚠️|📋|📖|⚡)\s*\*\*.*\*\*:?\s*$/gi.test(cleanParagraph);
        if (isHeader) {
          const match = cleanParagraph.match(/^(🔹|💡|⚠️|📋|📖|⚡)\s*\*\*(.*?)\*\*:?\s*$/);
          if (match) {
            const [, emoji, headerText] = match;
            const getHeaderStyle = (emoji) => {
              switch (emoji) {
                case '💡': return 'from-yellow-100 to-amber-100 dark:from-yellow-900/30 dark:to-amber-900/30 border-yellow-500';
                case '⚠️': return 'from-red-100 to-rose-100 dark:from-red-900/30 dark:to-rose-900/30 border-red-500';
                case '📋': return 'from-blue-100 to-indigo-100 dark:from-blue-900/30 dark:to-indigo-900/30 border-blue-500';
                case '📖': return 'from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 border-green-500';
                case '⚡': return 'from-purple-100 to-violet-100 dark:from-purple-900/30 dark:to-violet-900/30 border-purple-500';
                default: return 'from-violet-100 to-purple-100 dark:from-violet-900/30 dark:to-purple-900/30 border-violet-500';
              }
            };

            return (
              <div key={pIndex} className="mb-8 last:mb-0">
                <div className={`bg-gradient-to-r ${getHeaderStyle(emoji)} rounded-xl p-5 border-l-4 shadow-lg hover:shadow-xl transition-all duration-300`}>
                  <h3 className="text-xl font-bold text-slate-800 dark:text-slate-100 flex items-center gap-4">
                    <span className="text-3xl drop-shadow-sm">{emoji}</span>
                    <span className="flex-1">{headerText.trim()}</span>
                  </h3>
                </div>
              </div>
            );
          }
        }

        // Detect numbered lists (1. ... 2. ... 3. ...)
        if (/^(\d+\. .+)(?:\n\d+\. .+)*$/ms.test(cleanParagraph)) {
          // Split by lines that start with a number and dot
          const items = cleanParagraph.split(/\n(?=\d+\. )/).map(s => s.trim()).filter(Boolean);
          return (
            <ol key={pIndex} className="list-decimal ml-6 mb-4 last:mb-0 space-y-2 text-base leading-relaxed text-slate-700 dark:text-slate-200">
              {items.map((item, i) => (
                <li key={i}>{item.replace(/^\d+\.\s*/, '')}</li>
              ))}
            </ol>
          );
        }

        const isListItem = /^(\d+\.|[a-zA-Z]\.|[-•*])\s+/.test(cleanParagraph);
        if (isListItem) {
          const match = cleanParagraph.match(/^(\d+\.|[a-zA-Z]\.|[-•*])\s+(.*)$/);
          if (match) {
            const bullet = match[1];
            const content = match[2];
            return (
              <div key={pIndex} className="mb-5 last:mb-0 group">
                <div className="flex items-start gap-4 p-4 rounded-xl bg-gradient-to-r from-slate-50/80 to-white/80 dark:from-slate-800/50 dark:to-slate-700/50 border border-slate-200/60 dark:border-slate-600/40 shadow-sm hover:shadow-md hover:from-violet-50/80 hover:to-purple-50/80 dark:hover:from-violet-900/20 dark:hover:to-purple-900/20 transition-all duration-300">
                  <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-violet-500 to-purple-600 rounded-full flex items-center justify-center shadow-md group-hover:scale-110 transition-transform duration-300">
                    <span className="text-white font-bold text-sm">
                      {bullet.replace(/[.*]/, '•')}
                    </span>
                  </div>
                  <div className="flex-1 pt-1">
                    <p className="text-lg leading-relaxed text-slate-800 dark:text-slate-100 font-medium">
                      {content}
                    </p>
                  </div>
                </div>
              </div>
            );
          }
        }

        // Regular paragraph with enhanced styling
        return (
          <div key={pIndex} className="mb-6 last:mb-0">
            <div className="p-5 rounded-xl bg-gradient-to-br from-slate-50/90 to-white/90 dark:from-slate-800/60 dark:to-slate-700/60 border border-slate-200/70 dark:border-slate-600/50 shadow-sm hover:shadow-md transition-all duration-300">
              <p className="text-lg leading-relaxed text-slate-800 dark:text-slate-100 font-medium">
                {cleanParagraph}
              </p>
            </div>
          </div>
        );
      }).filter(Boolean);
    };

    return (
      <div className={`relative bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 border-2 border-violet-600/40 rounded-2xl overflow-hidden h-full flex flex-col shadow-2xl ring-2 ring-violet-400/30 ${className}`}>
        <div className="bg-gradient-to-r from-violet-600 via-purple-600 to-violet-700 px-6 py-3 text-white text-sm font-bold flex items-center gap-3 flex-shrink-0 shadow-lg">
          <div className="flex gap-1.5">
            <div className="w-3 h-3 bg-red-400 rounded-full shadow-sm"></div>
            <div className="w-3 h-3 bg-yellow-400 rounded-full shadow-sm"></div>
            <div className="w-3 h-3 bg-green-400 rounded-full shadow-sm"></div>
          </div>
          <span className="flex items-center gap-2">
            <span className="text-lg">📄</span>
            Document Content
          </span>
        </div>
        <div className="flex-1 min-h-0 bg-gradient-to-br from-slate-900 to-slate-800 p-6">
          {formatTextContent(code)}
        </div>
      </div>
    );
  }
};

// --- components/AIExplanationBox.jsx ---
const AIExplanationBox = ({ question, answer, category, className = "" }) => {
  const [explanation, setExplanation] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isVisible, setIsVisible] = useState(true);

  // Mock AI explanation generator
  const generateAIExplanation = useCallback((question, answer, category) => {
    setIsLoading(true);

    // Simulate API call delay
    setTimeout(() => {
      const explanations = {
        'OOP': [
          "🧠 **Object-Oriented Programming Insight:**\n\nThis concept is fundamental to Java's design philosophy. Understanding this helps you write more maintainable and scalable code.\n\n💡 **Key Learning Points:**\n• Think of classes as blueprints for real-world objects\n• Encapsulation protects data integrity\n• Inheritance promotes code reuse\n• Polymorphism enables flexible design patterns\n\n🎯 **Practice Tip:**\nTry creating your own class examples using familiar objects like cars, books, or students.",
          "🔍 **Deep Dive Analysis:**\n\nThis OOP principle is one of the four pillars that make Java powerful for enterprise applications.\n\n⚡ **Why This Matters:**\n• Reduces code duplication\n• Makes debugging easier\n• Enables team collaboration\n• Supports design patterns\n\n🚀 **Next Steps:**\nOnce you master this, explore how it connects to interfaces and abstract classes."
        ],
        'Basic Concepts': [
          "📚 **Foundational Knowledge:**\n\nThis is a core Java concept that every developer must understand thoroughly.\n\n🔹 **Memory Management:**\n• Understanding how Java handles object references\n• Stack vs Heap memory allocation\n• Garbage collection implications\n\n💭 **Common Mistakes:**\nMany beginners confuse reference comparison with value comparison. Practice with different scenarios to build intuition.",
          "🎓 **Learning Strategy:**\n\nThis concept appears frequently in interviews and real-world coding.\n\n⚠️ **Important Notes:**\n• Always use .equals() for String comparison\n• Be aware of null pointer exceptions\n• Understand autoboxing with primitives\n\n🔧 **Debugging Tip:**\nUse IDE debugger to visualize object references and see the difference in action."
        ],
        'Collections': [
          "📊 **Data Structure Wisdom:**\n\nJava Collections Framework is the backbone of efficient data handling.\n\n🔹 **Performance Considerations:**\n• Time complexity for different operations\n• Memory overhead comparisons\n• Thread safety implications\n\n💡 **Best Practices:**\nChoose the right collection based on your use case: ArrayList for random access, LinkedList for frequent insertions.",
          "🏗️ **Architecture Understanding:**\n\nCollections demonstrate Java's interface-based design beautifully.\n\n⚡ **Key Insights:**\n• Interface segregation principle in action\n• Generic type safety benefits\n• Iterator pattern implementation\n\n🎯 **Real-world Application:**\nMost enterprise applications heavily rely on collections for data processing and storage."
        ],
        'Error Handling': [
          "🛡️ **Defensive Programming:**\n\nException handling is crucial for building robust applications.\n\n🔹 **Exception Hierarchy:**\n• Checked vs unchecked exceptions\n• When to catch vs when to propagate\n• Custom exception design patterns\n\n⚠️ **Best Practices:**\n• Fail fast principle\n• Meaningful error messages\n• Proper resource cleanup",
          "🔧 **Error Recovery Strategies:**\n\nGood exception handling can make or break application reliability.\n\n💡 **Advanced Concepts:**\n• Exception chaining\n• Try-with-resources\n• Suppressed exceptions\n\n🎯 **Production Tip:**\nAlways log exceptions with sufficient context for debugging in production environments."
        ]
      };

      const categoryExplanations = explanations[category] || explanations['Basic Concepts'];
      const randomExplanation = categoryExplanations[Math.floor(Math.random() * categoryExplanations.length)];

      setExplanation(randomExplanation);
      setIsLoading(false);
    }, 1500);
  }, []);

  useEffect(() => {
    if (question && answer) {
      generateAIExplanation(question, answer, category);
    }
  }, [question, answer, category, generateAIExplanation]);

  return (
    <div className={`relative bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 border-2 border-emerald-600/40 rounded-2xl overflow-hidden h-full flex flex-col shadow-2xl ring-2 ring-emerald-400/30 ${className}`}>
      <div className="bg-gradient-to-r from-emerald-600 via-teal-600 to-emerald-700 px-4 py-2 text-white text-sm font-bold flex items-center justify-between shadow-lg">
        <span className="flex items-center gap-2">
          <Brain className="w-5 h-5" />
          AI Explanation
        </span>
        <button
          onClick={() => setIsVisible(prev => !prev)}
          className="p-1 hover:bg-white/10 rounded-lg transition-colors"
          aria-label={isVisible ? "Hide AI Explanation" : "Show AI Explanation"}
        >
          {isVisible ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
        </button>
      </div>
      <div className={`overflow-hidden transition-all duration-300 ${isVisible ? 'max-h-[600px]' : 'max-h-0'}`}>
        <div className="flex-1 min-h-0 bg-gradient-to-br from-slate-900 to-slate-800 p-6">
          {isLoading ? (
            <div className="flex flex-col items-center justify-center h-40 text-emerald-400">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-400 mb-4"></div>
              <p className="text-sm">Generating AI explanation...</p>
            </div>
          ) : (
            <div className="text-gray-100 text-sm leading-relaxed">
              <JavaCodeHighlighter code={explanation} />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// --- constants/gradingOptions.js ---
const GRADING_OPTIONS = [
  { label: "Again", grade: 1, color: "red", shortcut: "1", icon: ThumbsDown, arrowIcon: ArrowLeft, feedbackColor: "text-red-500" },
  { label: "Hard", grade: 2, color: "amber", shortcut: "2", icon: Meh, arrowIcon: ArrowDown, feedbackColor: "text-amber-500" },
  { label: "Good", grade: 3, color: "emerald", shortcut: "3", icon: ThumbsUp, arrowIcon: ArrowRight, feedbackColor: "text-emerald-500" },
  { label: "Easy", grade: 4, color: "sky", shortcut: "4", icon: Sparkles, arrowIcon: ArrowUp, feedbackColor: "text-sky-500" }
];

// --- Sample Flashcard Data ---
const INITIAL_CARDS = [
  {
    id: 1,
    question: "What is a React component?",
    answer: "A React component is a reusable piece of UI that can be defined as a JavaScript function or class.\n\nKey points:\n1. Accepts props as input\n2. Returns React elements (JSX)\n3. Can be functional or class-based\n\nExample (function):\nfunction Welcome(props) {\n  return <h1>Hello, {props.name}!</h1>;\n}",
    category: "React JS",
    difficulty: 3,
    stability: 2.5,
    state: 'new',
    reviewCount: 0,
    lastReview: null,
    dueDate: null
  },
  {
    id: 2,
    question: "What is JSX in React?",
    answer: "JSX is a syntax extension for JavaScript that looks similar to HTML and is used to describe UI in React.\n\nKey features:\n1. Allows writing HTML-like code in JavaScript\n2. Compiles to React.createElement() calls\n3. Can embed JavaScript expressions with {}\n\nExample:\nconst element = <h1>Hello, world!</h1>;\nconst name = 'React';\nconst greeting = <p>Hello, {name}!</p>;",
    category: "React JS",
    difficulty: 2,
    stability: 2.5,
    state: 'new',
    reviewCount: 0,
    lastReview: null,
    dueDate: null
  },
  {
    id: 3,
    question: "How do you manage state in a React function component?",
    answer: "State in a function component is managed using the useState hook.\n\nKey steps:\n1. Import useState from 'react'\n2. Call useState to declare state variables\n3. Update state using the setter function\n\nExample:\nimport { useState } from 'react';\nconst [count, setCount] = useState(0);\nsetCount(count + 1);",
    category: "React JS",
    difficulty: 3,
    stability: 2.5,
    state: 'new',
    reviewCount: 0,
    lastReview: null,
    dueDate: null
  },
  {
    id: 4,
    question: "What is the Virtual DOM in React?",
    answer: "The Virtual DOM is a lightweight JavaScript representation of the real DOM. React uses it to optimize UI updates.\n\nKey points:\n1. React creates a virtual DOM tree in memory\n2. On state change, React compares (diffs) the new virtual DOM with the previous one\n3. Only the changed parts are updated in the real DOM (efficient re-rendering)",
    category: "React JS",
    difficulty: 4,
    stability: 2.5,
    state: 'new',
    reviewCount: 0,
    lastReview: null,
    dueDate: null
  },
  {
    id: 5,
    question: "How do you pass data from a parent to a child component in React?",
    answer: "Data is passed from parent to child using props.\n\nKey steps:\n1. Parent adds a prop to the child component\n2. Child receives props as a parameter\n\nExample:\nfunction Parent() {\n  return <Child name=\"React\" />;\n}\nfunction Child(props) {\n  return <div>Hello, {props.name}!<\/div>;\n}",
    category: "React JS",
    difficulty: 2,
    stability: 2.5,
    state: 'new',
    reviewCount: 0,
    lastReview: null,
    dueDate: null
  },
  {
    id: 6,
    question: "What is useEffect used for in React?",
    answer: "useEffect is a React hook for performing side effects in function components.\n\nCommon uses:\n1. Data fetching\n2. Subscribing to events\n3. Updating the DOM\n\nExample:\nimport { useEffect } from 'react';\nuseEffect(() => {\n  document.title = 'Hello, React!';\n}, []);",
    category: "React JS",
    difficulty: 3,
    stability: 2.5,
    state: 'new',
    reviewCount: 0,
    lastReview: null,
    dueDate: null
  },
  {
    id: 7,
    question: "How do you handle events in React?",
    answer: "Events in React are handled using camelCase event props and passing a function.\n\nKey points:\n1. Use onClick, onChange, etc.\n2. Pass a function as the event handler\n3. Access the event object as a parameter\n\nExample:\nfunction Button() {\n  function handleClick(e) {\n    alert('Clicked!');\n  }\n  return <button onClick={handleClick}>Click me</button>;\n}",
    category: "React JS",
    difficulty: 2,
    stability: 2.5,
    state: 'new',
    reviewCount: 0,
    lastReview: null,
    dueDate: null
  },
  {
    id: 8,
    question: "What are React keys and why are they important?",
    answer: "Keys are special string attributes used to identify elements in a list.\n\nKey points:\n1. Help React identify which items have changed, are added, or are removed\n2. Should be unique among siblings\n3. Improve rendering performance\n\nExample:\nconst items = ['A', 'B', 'C'];\n<ul>\n  {items.map(item => <li key={item}>{item}</li>)}\n</ul>;",
    category: "React JS",
    difficulty: 3,
    stability: 2.5,
    state: 'new',
    reviewCount: 0,
    lastReview: null,
    dueDate: null
  },
  {
    id: 9,
    question: "How do you conditionally render content in React?",
    answer: "Conditional rendering in React is done using JavaScript expressions.\n\nCommon patterns:\n1. Ternary operator: {isLoggedIn ? <Logout /> : <Login />}\n2. Logical AND: {count > 0 && <span>Count: {count}</span>}\n3. if/else statements inside functions\n\nExample:\nfunction Greeting({ isLoggedIn }) {\n  return isLoggedIn ? <h1>Welcome!</h1> : <h1>Please log in.</h1>;\n}",
    category: "React JS",
    difficulty: 3,
    stability: 2.5,
    state: 'new',
    reviewCount: 0,
    lastReview: null,
    dueDate: null
  },
  {
    id: 10,
    question: "What is prop drilling and how can you avoid it?",
    answer: "Prop drilling is the process of passing data through many nested components via props.\n\nProblems:\n1. Makes code harder to maintain\n2. Can lead to unnecessary re-renders\n\nSolutions:\n• Use React Context to share data globally\n• Use state management libraries (Redux, Zustand, etc.)\n\nExample (Context):\nconst MyContext = React.createContext();\nfunction Parent() {\n  return <MyContext.Provider value=\"data\"><Child /><\/MyContext.Provider>;\n}",
    category: "React JS",
    difficulty: 4,
    stability: 2.5,
    state: 'new',
    reviewCount: 0,
    lastReview: null,
    dueDate: null
  },
  {
    id: 11,
    question: "What is the difference between controlled and uncontrolled components in React?",
    answer: "Controlled components are React components whose form data is handled by the React state, while uncontrolled components store their own state internally (using refs).\n\nKey points:\n1. Controlled: value and onChange are managed by React\n2. Uncontrolled: use defaultValue and refs to access values\n\nExample (controlled):\n<input value={value} onChange={e => setValue(e.target.value)} />\n\nExample (uncontrolled):\n<input defaultValue=\"initial\" ref={inputRef} />",
    category: "React JS",
    difficulty: 3,
    stability: 2.5,
    state: 'new',
    reviewCount: 0,
    lastReview: null,
    dueDate: null
  },
  {
    id: 12,
    question: "How does React Context work and when should you use it?",
    answer: "React Context provides a way to share values (like state or functions) between components without passing props manually at every level.\n\nKey points:\n1. Create a context with React.createContext()\n2. Use a Provider to supply the value\n3. Use useContext to consume the value\n\nExample:\nconst MyContext = React.createContext();\nfunction App() {\n  return <MyContext.Provider value=\"data\"><Child /></MyContext.Provider>;\n}\nfunction Child() {\n  const value = useContext(MyContext);\n  return <div>{value}</div>;\n}",
    category: "React JS",
    difficulty: 3,
    stability: 2.5,
    state: 'new',
    reviewCount: 0,
    lastReview: null,
    dueDate: null
  },
  {
    id: 13,
    question: "What is the useRef hook and what are common use cases?",
    answer: "useRef returns a mutable ref object whose .current property persists across renders.\n\nCommon uses:\n1. Accessing DOM elements\n2. Storing mutable values that don't trigger re-renders\n3. Keeping track of previous values\n\nExample:\nconst inputRef = useRef();\n<input ref={inputRef} />\ninputRef.current.focus();",
    category: "React JS",
    difficulty: 3,
    stability: 2.5,
    state: 'new',
    reviewCount: 0,
    lastReview: null,
    dueDate: null
  },
  {
    id: 14,
    question: "How do you optimize performance in a large React application?",
    answer: "Performance optimization techniques include:\n1. Code splitting with React.lazy and Suspense\n2. Memoization with React.memo, useMemo, and useCallback\n3. Avoiding unnecessary re-renders\n4. Virtualizing long lists (e.g., react-window)\n5. Using the production build\n\nExample (memoization):\nconst memoizedValue = useMemo(() => computeExpensiveValue(a, b), [a, b]);",
    category: "React JS",
    difficulty: 4,
    stability: 2.5,
    state: 'new',
    reviewCount: 0,
    lastReview: null,
    dueDate: null
  },
  {
    id: 15,
    question: "What is React.memo and when should you use it?",
    answer: "React.memo is a higher-order component that memoizes a functional component, preventing unnecessary re-renders if props haven't changed.\n\nKey points:\n1. Use for pure functional components\n2. Helps with performance in large lists\n\nExample:\nconst MyComponent = React.memo(function MyComponent(props) {\n  /* render using props */\n});",
    category: "React JS",
    difficulty: 3,
    stability: 2.5,
    state: 'new',
    reviewCount: 0,
    lastReview: null,
    dueDate: null
  },
  {
    id: 16,
    question: "How do you implement error boundaries in React?",
    answer: "Error boundaries are React components that catch JavaScript errors in their child component tree.\n\nKey points:\n1. Must be class components\n2. Implement componentDidCatch and getDerivedStateFromError\n\nExample:\nclass ErrorBoundary extends React.Component {\n  state = { hasError: false };\n  static getDerivedStateFromError(error) {\n    return { hasError: true };\n  }\n  componentDidCatch(error, info) {\n    // log error\n  }\n  render() {\n    if (this.state.hasError) return <h1>Something went wrong.</h1>;\n    return this.props.children;\n  }\n}",
    category: "React JS",
    difficulty: 4,
    stability: 2.5,
    state: 'new',
    reviewCount: 0,
    lastReview: null,
    dueDate: null
  },
  {
    id: 17,
    question: "What is the difference between useEffect and useLayoutEffect?",
    answer: "useEffect runs after the DOM has been painted, while useLayoutEffect runs synchronously after all DOM mutations but before the browser paints.\n\nKey points:\n1. useEffect: non-blocking, for side effects\n2. useLayoutEffect: blocking, for DOM measurements\n\nExample:\nuseLayoutEffect(() => {\n  // Read layout or measure DOM\n});",
    category: "React JS",
    difficulty: 4,
    stability: 2.5,
    state: 'new',
    reviewCount: 0,
    lastReview: null,
    dueDate: null
  },
  {
    id: 18,
    question: "How do you perform form validation in React?",
    answer: "Form validation can be done manually or with libraries.\n\nKey points:\n1. Track form state with useState\n2. Validate on change or submit\n3. Use libraries like Formik or React Hook Form for complex forms\n\nExample (manual):\nconst [email, setEmail] = useState('');\nconst isValid = email.includes('@');",
    category: "React JS",
    difficulty: 3,
    stability: 2.5,
    state: 'new',
    reviewCount: 0,
    lastReview: null,
    dueDate: null
  },
  {
    id: 19,
    question: "What are custom hooks and why are they useful?",
    answer: "Custom hooks are functions that use React hooks to encapsulate reusable logic.\n\nKey points:\n1. Start with 'use' (e.g., useFetch)\n2. Promote code reuse and separation of concerns\n\nExample:\nfunction useCounter(initial) {\n  const [count, setCount] = useState(initial);\n  const increment = () => setCount(c => c + 1);\n  return { count, increment };\n}",
    category: "React JS",
    difficulty: 3,
    stability: 2.5,
    state: 'new',
    reviewCount: 0,
    lastReview: null,
    dueDate: null
  },
  {
    id: 20,
    question: "How do you implement code splitting in a React app?",
    answer: "Code splitting allows you to load parts of your app on demand.\n\nKey points:\n1. Use React.lazy for dynamic imports\n2. Wrap lazy components with Suspense\n\nExample:\nconst OtherComponent = React.lazy(() => import('./OtherComponent'));\n<Suspense fallback={<div>Loading...</div>}>\n  <OtherComponent />\n</Suspense>;",
    category: "React JS",
    difficulty: 4,
    stability: 2.5,
    state: 'new',
    reviewCount: 0,
    lastReview: null,
    dueDate: null
  }
];

// --- Main Flashcard App Component ---
const JavaFlashcardApp = () => {
  const [cards, setCards] = useState(INITIAL_CARDS);
  const [currentIdx, setCurrentIdx] = useState(0);
  const [showAnswer, setShowAnswer] = useState(false);
  const [showCode, setShowCode] = useState(true);
  const [showAIExplanation, setShowAIExplanation] = useState(true);
  const [feedback, setFeedback] = useState(null);

  const handleGrade = useCallback((grade) => {
    setFeedback(GRADING_OPTIONS.find(opt => opt.grade === grade));
    setTimeout(() => {
      setFeedback(null);
      setShowAnswer(false);
      setShowCode(true);
      setCurrentIdx(idx => (idx + 1) % cards.length);
    }, 600);
  }, [cards.length]);

  useEffect(() => {
    setShowAnswer(false);
    setFeedback(null);
    setShowCode(true);
    setShowAIExplanation(true);
  }, [currentIdx]);

  // Keyboard shortcuts for grading
  useEffect(() => {
    const handleKey = (e) => {
          if (e.repeat) return;
          if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.metaKey || e.ctrlKey) return;

          if (!showAnswer && (e.key === ' ' || e.key === 'Enter')) {
            e.preventDefault();
            setShowAnswer(true);
          } else if (showAnswer) {
            const opt = GRADING_OPTIONS.find(o => o.shortcut === e.key);
            if (opt) {
              e.preventDefault();
              handleGrade(opt.grade);
            }
          }

          if (e.key === 'ArrowLeft') {
            e.preventDefault();
            setCurrentIdx(prevIdx => (prevIdx === 0 ? cards.length - 1 : prevIdx - 1));
            setShowAnswer(false);
          } else if (e.key === 'ArrowRight') {
            e.preventDefault();
            setCurrentIdx(prevIdx => (prevIdx === cards.length - 1 ? 0 : prevIdx + 1));
            setShowAnswer(false);
          }
        };
    window.addEventListener('keydown', handleKey);
    return () => window.removeEventListener('keydown', handleKey);
  }, [showAnswer, cards, currentIdx, handleGrade]);

  if (!cards.length) {
    return <div className="flex items-center justify-center min-h-screen text-2xl font-bold text-violet-700">No cards available.</div>;
  }

  // Helper function to detect if answer contains code
  const hasCodeInAnswer = (answer) => {
    const codePatterns = [
      /\bclass\s+\w+\s*\{/,
      /\bpublic\s+static\s+void\s+main/,
      /System\.out\.println/,
      /\w+\s*\([^)]*\)\s*\{/,
      /import\s+[\w.]+;/,
      /\bpublic\s+\w+\s+\w+\s*\(/
    ];
    return codePatterns.some(pattern => pattern.test(answer));
  };

  // Split answer into text and code parts
  const splitAnswerAndCode = (answer) => {
    const lines = answer.split('\n');
    const textLines = [];
    const codeLines = [];
    let inCodeBlock = false;

    for (const line of lines) {
      if (line.includes('Example:') || line.includes('Code:') || hasCodeInAnswer(line)) {
        inCodeBlock = true;
      }

      if (inCodeBlock) {
        codeLines.push(line);
      } else {
        textLines.push(line);
      }
    }

    return {
      text: textLines.join('\n').trim(),
      code: codeLines.join('\n').trim()
    };
  };

  const card = cards[currentIdx];
  const { text: answerText, code: answerCode } = showAnswer ? splitAnswerAndCode(card.answer) : { text: '', code: '' };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-violet-50 to-purple-100 dark:from-slate-900 dark:to-slate-950 p-4">
      <div className="w-full max-w-7xl mx-auto flex gap-4">
        {/* Main card content */}
        <div className="flex-1 bg-white/70 dark:bg-slate-800/70 backdrop-blur-md rounded-2xl p-6 shadow-xl border border-violet-200/50 dark:border-purple-700/50">
          <div className="mb-6">
            <span className="text-xs px-3 py-1.5 bg-gradient-to-r from-violet-500 to-purple-600 text-white rounded-full font-semibold shadow-md">{card.category}</span>
            <span className="ml-2 text-xs text-slate-400 dark:text-slate-500 bg-slate-100/50 dark:bg-slate-700/50 px-2 py-1 rounded-lg">
              S:{card.stability?.toFixed(1)} D:{card.difficulty?.toFixed(1)} R:{card.reviewCount}
            </span>
          </div>
          <div className="text-xl md:text-2xl font-semibold text-slate-700 dark:text-slate-200 mb-4 prose max-w-none whitespace-pre-wrap break-words leading-relaxed pb-4 border-b border-slate-200 dark:border-slate-700">
            {card.question}
          </div>
          <div className="mt-4 min-h-[200px]">
            {showAnswer ? (
              <JavaCodeHighlighter code={answerText} className="mb-4" />
            ) : (
              <button
                onClick={() => setShowAnswer(true)}
                className="w-full bg-gradient-to-r from-violet-600 via-purple-600 to-indigo-600 text-white py-4 rounded-2xl hover:from-violet-700 hover:via-purple-700 hover:to-indigo-700 transition-all duration-300 font-bold text-lg shadow-xl hover:shadow-2xl transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-purple-400 focus:ring-offset-2 dark:ring-offset-slate-900"
                aria-label="Show Answer (Space)"
              >
                ✨ Show Answer <span className="ml-2 text-xs text-purple-200 bg-white/20 px-2 py-1 rounded-full">[Space]</span>
              </button>
            )}
          </div>
          {showAnswer && (
            <div className="grid grid-cols-2 gap-3 mt-6">
              {GRADING_OPTIONS.map(opt => (
                <button
                  key={opt.grade}
                  className={`bg-gradient-to-r ${
                    opt.color === 'red' ? 'from-red-500 to-rose-600 hover:from-red-600 hover:to-rose-700' :
                    opt.color === 'amber' ? 'from-amber-500 to-orange-600 hover:from-amber-600 hover:to-orange-700' :
                    opt.color === 'emerald' ? 'from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700' :
                    'from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700'
                  } text-white py-3 rounded-xl font-bold shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white/50`}
                  onClick={() => handleGrade(opt.grade)}
                  aria-label={`${opt.label} (${opt.shortcut})`}
                >
                  <opt.arrowIcon className="inline h-4 w-4 mr-1.5" />
                  {opt.label}
                  <span className="ml-1 text-xs bg-white/20 px-1.5 py-0.5 rounded-full">[{opt.shortcut}]</span>
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Code box on the right */}
        {showAnswer && answerCode && (
          <div className="w-[400px] flex-shrink-0">
            <div className="bg-white/70 dark:bg-slate-800/70 backdrop-blur-md rounded-2xl shadow-xl border border-violet-200/50 dark:border-purple-700/50 overflow-hidden">
              <div className="bg-gradient-to-r from-violet-600 via-purple-600 to-violet-700 px-4 py-2 text-white text-sm font-bold flex items-center justify-between shadow-lg">
                <span className="flex items-center gap-2">
                  <span className="text-lg">💻</span>
                  Code Example
                </span>
                <button
                  onClick={() => setShowCode(prev => !prev)}
                  className="p-1 hover:bg-white/10 rounded-lg transition-colors"
                  aria-label={showCode ? "Hide Code Example" : "Show Code Example"}
                >
                  {showCode ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
                </button>
              </div>
              <div className={`overflow-hidden transition-all duration-300 ${showCode ? 'max-h-[600px]' : 'max-h-0'}`}>
                {showCode && <JavaCodeHighlighter code={answerCode} />}
              </div>
            </div>
          </div>
        )}

        {/* AI Explanation box */}
        {showAnswer && (
          <div className="w-[400px] flex-shrink-0">
            <AIExplanationBox
              question={card.question}
              answer={card.answer}
              category={card.category}
            />
          </div>
        )}
      </div>

      {feedback && (
        <div className={`fixed inset-0 flex items-center justify-center z-50 pointer-events-none transition-opacity duration-300 ${feedback ? 'opacity-100' : 'opacity-0'}`}>
          <div className={`p-8 rounded-2xl shadow-2xl text-3xl font-bold flex flex-col items-center gap-2 bg-white/90 dark:bg-slate-900/90 ${feedback.feedbackColor}`}>
            <feedback.icon size={48} />
            <span>{feedback.label}</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default JavaFlashcardApp;